<div class="user-layout">
  <!-- <div class="header">
    <h2>
      使用者設置
      <i class="material-icons" *ngIf="false" [appTip]="'使用者設置'">help_outline</i>
    </h2>
    <i class="material-icons close" (click)="close()">clear</i>
  </div> -->
  <span class="user-search">
    <span class="search">
      <label>搜尋：</label>
      <input type="text" [(ngModel)]="keyword" (keyup.enter)="search()" />
    </span>

    <select #role (change)="selectROLE(role.value)" *ngIf="false">
      <option [value]="null">無</option>
      <option *ngFor="let role of roleList" [value]="role.id">
        {{ role.name }}
      </option>
    </select>

    <span class="btns" *ngIf="false">
      <button mat-raised-button>
        <i class="material-icons">add</i>匯入使用者
      </button>
    </span>
    &nbsp;
    <span class="btns">
      <button mat-raised-button (click)="createUser()">
        <i class="material-icons">add</i>建立
      </button>
      <button mat-raised-button (click)="registrationSetting()" *ngIf="false">
        註冊設置
      </button>
    </span>
  </span>
  <span class="user-search">
    <label
      >匯入使用者：<a
        mat-raised-button
        href="assets/files/使用者資料範例檔.xlsx"
        target="_blank"
        >下載使用者資料範例檔</a
      ></label
    >
  </span>
  <span class="user-search">
    <label>選擇Excel檔案：</label>
    <input type="file" accept=".xlsx" (change)="inputAttachments($event)" />
    &nbsp;
    <button mat-raised-button (click)="importUser()">匯入Excel</button>
  </span>
  <span class="user-list">
    <span class="user-block" *ngFor="let item of userList">
      <!-- <span class="user-block" *ngFor="let item of userList" (contextmenu)="onContextMenu($event, item)"></span> -->
      <span class="user-name">
        {{ item.name }}
        <span style="margin-left: auto">
          <button mat-raised-button (click)="login(item)">登入次數</button
          >&nbsp;
          <button mat-raised-button (click)="editUser(item)">編輯</button>&nbsp;
          <button mat-raised-button (click)="delete(item)">刪除</button>&nbsp;
        </span>

        <mat-slide-toggle
          color="primary"
          (change)="updateEnable($event, item)"
          [checked]="item.enable"
        >
        </mat-slide-toggle>
      </span>
    </span>
  </span>
  <span class="lazyload">
    <button
      mat-icon-button
      (click)="loadMore()"
      *ngIf="userList.length < totalCount"
    >
      <mat-icon>keyboard_arrow_down</mat-icon>
    </button>
  </span>
  <app-loading [loading]="loading"></app-loading>
</div>
